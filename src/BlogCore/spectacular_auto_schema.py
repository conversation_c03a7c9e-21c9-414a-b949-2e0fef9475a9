# myproject/spectacular_auto_schema.py
from drf_spectacular.openapi import AutoSchema
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers

class CustomAutoSchema(AutoSchema):
    def _map_serializer_field(self, field, direction, bypass_extensions=False):
        field_name = getattr(field, 'field_name', None)
        print(f"[DEBUG] Field: {field_name}, Type: {type(field)}")
        if isinstance(field, (serializers.ImageField, serializers.FileField)):
            return {
                'type': 'string',
                'format': 'binary',
            }
        return super()._map_serializer_field(field, direction, bypass_extensions)
